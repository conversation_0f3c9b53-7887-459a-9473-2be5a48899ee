# Installation
> `npm install --save @types/bootstrap`

# Summary
This package contains type definitions for bootstrap (https://getbootstrap.com/).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/bootstrap.

### Additional Details
 * Last updated: Wed, 22 Nov 2023 00:24:48 GMT
 * Dependencies: [@popperjs/core](https://npmjs.com/package/@popperjs/core)

# Credits
These definitions were written by [denisname](https://github.com/denisname), [<PERSON><PERSON><PERSON>](https://github.com/peter<PERSON><PERSON><PERSON>), [<PERSON>](https://github.com/martin-badin), [<PERSON>](https://github.com/kyletsang), and [<PERSON>](https://github.com/luc122c).
