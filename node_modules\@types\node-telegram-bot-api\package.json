{"name": "@types/node-telegram-bot-api", "version": "0.64.9", "description": "TypeScript definitions for node-telegram-bot-api", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node-telegram-bot-api", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "ammu<PERSON><PERSON>", "url": "https://github.com/ammuench"}, {"name": "Agadar", "githubUsername": "aga<PERSON>", "url": "https://github.com/agadar"}, {"name": "<PERSON>", "githubUsername": "Dabol<PERSON>", "url": "https://github.com/Dabolus"}, {"name": "XC-Zhang", "githubUsername": "XC-Zhang", "url": "https://github.com/XC-Zhang"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "githubUsername": "adity<PERSON><PERSON><PERSON>", "url": "https://github.com/adityathebe"}, {"name": "XieJiSS", "githubUsername": "XieJiSS", "url": "https://github.com/XieJiSS"}, {"name": "<PERSON><PERSON>", "githubUsername": "toniop99", "url": "https://github.com/toniop99"}, {"name": "Konstantin24121", "githubUsername": "konstantin24121", "url": "https://github.com/konstantin24121"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/node-telegram-bot-api"}, "scripts": {}, "dependencies": {"@types/node": "*", "@types/request": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "d397e5ed4bbfd9438add03d9417aae777d29811af2deee03873e1d9c2b9f1d5d", "typeScriptVersion": "5.1"}