# Installation
> `npm install --save @types/play-sound`

# Summary
This package contains type definitions for play-sound (https://github.com/shime/play-sound).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/play-sound.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/play-sound/index.d.ts)
````ts
/// <reference types="node" />

import { ChildProcess, ExecException } from "child_process";

type Players = "mplayer" | "afplay" | "mpg123" | "mpg321" | "play" | "omxplayer" | "aplay" | "cmdmp3";

interface PlayOpts {
    players: Players[];
    player: Players;
}

type PlayMethodOptions = Partial<
    {
        [key in Players]: Array<string | number>;
    } & {
        timeout: number;
    }
>;

declare class Play {
    constructor(opts?: Partial<PlayOpts>);

    player: unknown;
    players: Players[];
    urlRegex: RegExp;

    play(what: string, options?: PlayMethodOptions, next?: (err: ExecException) => void): ChildProcess;
    play(what: string, next?: (err: ExecException) => void): ChildProcess;

    test(next?: (err: ExecException) => void): ChildProcess;
}

declare function defaultExport(opts?: Partial<PlayOpts>): Play;

export = defaultExport;

````

### Additional Details
 * Last updated: Tue, 07 Nov 2023 09:09:39 GMT
 * Dependencies: [@types/node](https://npmjs.com/package/@types/node)

# Credits
These definitions were written by [Corbin Crutchley](https://github.com/crutchcorn).
